#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
顺丰URL测试脚本
用于测试URL是否能正常登录并获取用户信息
"""

import requests
from urllib.parse import unquote
import json

def test_sf_url(url):
    """测试顺丰URL是否有效"""
    print(f"测试URL: {url[:100]}...")

    # 创建session
    session = requests.Session()
    session.verify = False

    # 设置请求头（模拟微信小程序）
    headers = {
        'Host': 'mcs-mimp-web.sf-express.com',
        'upgrade-insecure-requests': '1',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090551) XWEB/6945 Flue',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
        'sec-fetch-site': 'none',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-user': '?1',
        'sec-fetch-dest': 'document',
        'accept-language': 'zh-CN,zh',
        'platform': 'MINI_PROGRAM',
    }
    
    try:
        # 解码URL
        decoded_url = unquote(url)
        print(f"📝 解码后URL: {decoded_url[:100]}...")
        
        # 发送请求
        response = session.get(decoded_url, headers=headers, timeout=30)
        print(f"📊 响应状态码: {response.status_code}")
        
        # 检查Cookie
        cookies = session.cookies.get_dict()
        print(f"🍪 获取到的Cookie:")
        for key, value in cookies.items():
            print(f"  {key}: {value[:50]}...")
        
        # 检查关键Cookie
        user_id = cookies.get('_login_user_id_', '')
        phone = cookies.get('_login_mobile_', '')
        
        if user_id and phone:
            masked_phone = phone[:3] + "*" * 4 + phone[7:] if len(phone) >= 7 else phone
            print(f"✅ 登录成功!")
            print(f"👤 用户ID: {user_id}")
            print(f"📱 手机号: {masked_phone}")
            return True
        else:
            print(f"❌ 登录失败 - 未获取到用户信息")
            print(f"📄 响应内容前500字符:")
            print(response.text[:500])
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

def main():
    print("=" * 50)
    print("顺丰速运URL测试工具")
    print("=" * 50)

    # 你的URL
    test_url = "https://mcs-mimp-web.sf-express.com/mcs-mimp/share/weChat/activityRedirect?source=CX&unionId=YQ1N3lUDNaSg9Ox9%2BBwNXRFFsW2OBAKilgqhG8pgOBo%3D&openId=s1G5ik6AWFIQQaL5sshp%2FZwTIdtITonvNlva7jmu380%3D&memId=hych5uZiPmTZK%2FpoxrKOmetUH%2BV8uGn%2F%2FYU88EDDho4DdjSr%2F6X0WoiZtgGzs7sG&memNo=6tB15T6k0ZvyroDMcHJm6TxGNOheCN75HuG7rOc5Fw4DdjSr%2F6X0WoiZtgGzs7sG&mobile=EpJ84tf34s%2B0ot4Y2kXwuA%3D%3D&mediaCode=wxapp&bizCode=%7B%22path%22%3A%22%2Fup-member%2FnewHome%22%2C%22supportShare%22%3A%22NO%22%2C%22maskType%22%3A%22autoReceive%22%2C%22from%22%3A%22surprise_benefitwxauto%22%2C%22equityKey%22%3A%22surprise_benefit%22%7D&citycode=833&cityname=%E4%B9%90%E5%B1%B1&cx-at-sign=BA3A58B406AF0580728038A1FE486B2C4D52A13288EE6057715F83DD26392367&cx-at-ts=1754409232&cx-at-nonce=act0zf_9CAvIYpebqBICe&t=1754409232"

    print("开始测试URL...")
    result = test_sf_url(test_url)

    if not result:
        print("\n建议:")
        print("1. 检查URL是否完整且有效")
        print("2. 确认URL中的时间戳是否过期")
        print("3. 尝试重新获取最新的登录URL")
        print("4. 检查网络连接是否正常")

if __name__ == "__main__":
    main()
